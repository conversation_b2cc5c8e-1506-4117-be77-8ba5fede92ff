import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Button, IconButton, Radio, RadioGroup, FormControlLabel } from "@mui/material";
import { ManageListingWithDisplayGroupedDataAndMediaDto } from "redi-types";
import './styles.scss';

interface Props {
    yard: ManageListingWithDisplayGroupedDataAndMediaDto;
}

export function YardDetails(props: Props) {
    const { yard } = props;

    return (
        <div styleName="container">
            <div styleName="row">
                <div styleName="header">Yard Information</div>
                <Button 
                    variant="contained" 
                    color="primary"
                    styleName="exit-button"
                >
                    Exit the yard
                </Button>
            </div>
            <div styleName="grid">
                <div styleName="column">
                    <div styleName="label">Current Yard</div>
                    <div styleName="value">{yard.fields?.Yard}</div>
                </div>
                <div styleName="column">
                    <div styleName="label">Invoice No</div>
                    <div styleName="value">
                        {yard.fields?.InvoiceNo}
                        {yard.fields?.InvoiceLink && (
                            <a href={yard.fields.InvoiceLink} target="_blank" rel="noopener noreferrer">View Copy</a>
                        )}
                    </div>
                </div>
                <div styleName="column">
                    <div styleName="label">Rego</div>
                    <div styleName="value">{yard.fields?.CarRegistration}</div>
                </div>
                <div styleName="column">
                    <div styleName="label">Car Make/Model</div>
                    <div styleName="value">{yard.fields?.CarMakeModel}</div>
                </div>
                <div styleName="column">
                    <div styleName="label">Customer Name</div>
                    <div styleName="value">{yard.fields?.CustomerName}</div>
                </div>
                <div styleName="column">
                    <div styleName="label">Customer Phone</div>
                    <div styleName="value">{yard.fields?.CustomerPhone}</div>
                </div>
            </div>

            <div styleName="section">
                <div styleName="sub-header">Insurance Details</div>
                <div styleName="grid">
                    <div styleName="column">
                        <div styleName="label">Insurer</div>
                        <div styleName="value">{yard.fields?.Insurer}</div>
                    </div>
                    <div styleName="column">
                        <div styleName="label">Claim No</div>
                        <div styleName="value">{yard.fields?.ClaimNumber}</div>
                    </div>
                    <div styleName="column">
                        <div styleName="label">Notes</div>
                        <div styleName="value">{yard.fields?.Notes}</div>
                    </div>
                </div>
            </div>

            <div styleName="section">
                <div styleName="sub-header">Transfer Details</div>
                <div styleName="grid">
                    <div styleName="column">
                        <div styleName="label">Transfer Authorised</div>
                        <RadioGroup 
                            row 
                            value={yard.fields?.TransferAuthorised ? "yes" : "no"}
                        >
                            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
                            <FormControlLabel value="no" control={<Radio />} label="No" />
                        </RadioGroup>
                    </div>
                    <div styleName="column">
                        <div styleName="label">Date of Transfer</div>
                        <div styleName="value">{yard.fields?.TransferDate}</div>
                    </div>
                    <div styleName="column">
                        <div styleName="label">Location Transfer</div>
                        <div styleName="value">{yard.fields?.LocationTransfer}</div>
                    </div>
                    <div styleName="column">
                        <div styleName="label">Truck Allocated</div>
                        <div styleName="value">{yard.fields?.TruckAllocated}</div>
                    </div>
                    <div styleName="column">
                        <div styleName="label">Allocated By</div>
                        <div styleName="value">{yard.fields?.AllocatedBy}</div>
                    </div>
                    <div styleName="column">
                        <div styleName="label">Completed</div>
                        <RadioGroup 
                            row 
                            value={yard.fields?.Completed ? "yes" : "no"}
                        >
                            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
                            <FormControlLabel value="no" control={<Radio />} label="No" />
                        </RadioGroup>
                    </div>
                </div>
            </div>

            <div styleName="section">
                <div styleName="sub-header">Sub Job Details</div>
                <div styleName="grid">
                    <div styleName="column">
                        <div styleName="label">Job Number</div>
                        <div styleName="value">{yard.fields?.JobNumber}</div>
                    </div>
                    <div styleName="column">
                        <div styleName="label">Date of Job</div>
                        <div styleName="value">{yard.fields?.JobDate}</div>
                    </div>
                    <div styleName="column">
                        <div styleName="label">Status</div>
                        <div styleName="value">{yard.fields?.JobStatus}</div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default YardDetails;
