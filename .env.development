REACT_APP_FAKE_KEY=DEV_KEY
REACT_APP_GOOGLE_API_KEY=AIzaSyDazYQu9BcKBsVWAmWoe-K6FNGZaI4aFyM
REACT_APP_API_BASE_URL=https://autotow.redi3.dev/listingapi
REACT_APP_COMMON_MICRO_URL=commoncomponents@https://autotow.redi3.dev/microfrontendcommon/commoncomponents.js
REACT_APP_CRM_MICRO_URL=crmcomponents@https://autotow.redi3.dev/microfrontendcrm/crmcomponents.js
DOCKERHUB_USERNAME=jadredi
DOCKERHUB_NAMESPACE=redisoftware
DOCKER_REPO=autotow-microfrontend-listingmgt
IMAGETAGVERSION=dev-latest
AZURE_GLOBAL_RESOURCE_GROUP=test_redi
AZURE_RESOURCE_GROUP=test-autotow
AZURE_TENANT_ID=ee84645d-1f03-4d0e-8e54-377dbf082ab2
AZURE_SERVICE_PRINCIPAL_ID=ddbc6916-4198-479d-bed6-c0f3fa23a294
AZURE_APP_ID=ddbc6916-4198-479d-bed6-c0f3fa23a294
AZURE_ACCOUNT_STORAGE=testredi1storage
AZURE_STORAGE_VOLUME=autotow-traefik-files-volume
SYSTEM_ENVIRONMENT=development
AZURE_ENVIRONMENT_NAME=test-autotow
AZURE_IDENTITY_NAME=test-redi-managed-identity