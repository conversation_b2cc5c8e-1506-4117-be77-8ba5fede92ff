import { TableCell } from "@mui/material";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { ListingWithDataAndMediaCDto } from "redi-types";
import './styles.scss';
import DataTable, { TableHeader } from "../../../components/DataTable/DataTable";
import ListingService from "../../../services/listing";
import { ListingTypeEnum } from "../../../enum/listingTypeEnum";

interface Props {}

export function YardList(props: Props) {
  const navigate = useNavigate();
  const [refreshTableTrigger, setRefreshTableTrigger] = useState(0);

  const tableHeaders: TableHeader[] = [
    { id: "createdOn", label: "Creation On", isSortable: true },
    { id: "JobNumber", label: "Job Number" },
    { id: "TowType", label: "Tow Type" },
    { id: "CarRegistration", label: "Rego" },
    { id: "CustomerName", label: "Customer Name" },
    { id: "TransferAuthorised", label: "Transfer Authorised" },
    { id: "TowedTo", label: "Location Transfer" },
    { id: "TruckId", label: "Truck Allocated" },
    { id: "AllocatedBy", label: "Allocated By" }
  ];

  function renderTableRow(data: ListingWithDataAndMediaCDto) {
    return (
      <>
        <TableCell>{data.createdOn}</TableCell>
        <TableCell>{data.fields?.JobNumber}</TableCell>
        <TableCell>{data.fields?.TowType}</TableCell>
        <TableCell>{data.fields?.CarRegistration}</TableCell>
        <TableCell>{data.fields?.CustomerName}</TableCell>
        <TableCell>{data.fields?.TransferAuthorised ? 'Yes' : 'No'}</TableCell>
        <TableCell>{data.fields?.TowedTo}</TableCell>
        <TableCell>{data.fields?.TruckId}</TableCell>
        <TableCell>{data.fields?.AllocatedBy}</TableCell>
      </>
    );
  }

  return (
    <div styleName="no-overflow">
      <div styleName="card">
        <DataTable<ListingWithDataAndMediaCDto, "listingId">
          primaryKeyProperty="listingId"
          title="Yard Log"
          tableId="base-yard-list"
          pageSize={10}
          initialSortColumn="creationDate"
          refreshTableTrigger={refreshTableTrigger}
          tableHeight={600}
          tableHeaders={tableHeaders}
          renderTableRow={renderTableRow}
          onRowClick={(row) => navigate(`/Yard/${row.listingId}`)}
          callService={(params, search) => ListingService.getList(
            undefined,
            undefined,
            "JobDetail",
            undefined,
            undefined,
            undefined,
            undefined,
            undefined,
            undefined,
            ListingTypeEnum.Job,
            search,
            undefined,
            undefined,
            undefined,
            params
          )}
        />
      </div>
    </div>
  );
}

export default YardList;