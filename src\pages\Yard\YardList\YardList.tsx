import { <PERSON>ert, AlertColor, TableCell, Snackbar } from "@mui/material";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { ListingWithDataAndMediaCDto } from "redi-types";
import './styles.scss';
import DataTable, { TableHeader } from "../../../components/DataTable/DataTable";
import DraggableDialog from "../../../components/DraggableDialog/DraggableDialog";
import { YardForm } from "../YardForm/YardForm";
import ListingService from "../../../services/listing";
import { ListingTypeEnum } from "../../../enum/listingTypeEnum";

interface Props {}

export function YardList(props: Props) {
  const navigate = useNavigate();
  const [refreshTableTrigger, setRefreshTableTrigger] = useState(0);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState<AlertColor | undefined>("success");

  const tableHeaders: TableHeader[] = [
    { id: "createdOn", label: "Creation On", isSortable: true },
    { id: "JobNumber", label: "Job Number" },
    { id: "TowType", label: "Tow Type" },
    { id: "CarRegistration", label: "Rego" },
    { id: "CustomerName", label: "Customer Name" },
    { id: "TransferAuthorised", label: "Transfer Authorised" },
    { id: "TowedTo", label: "Location Transfer" },
    { id: "TruckId", label: "Truck Allocated" },
    { id: "AllocatedBy", label: "Allocated By" }
  ];

  function closeAddDialog() {
    setIsAddDialogOpen(false);
  }

  function onAddDialogSave() {
    setRefreshTableTrigger(prev => prev + 1);
    setIsAddDialogOpen(false);
    setSnackbarMessage("Yard log created successfully");
    setSnackbarSeverity("success");
    setShowSnackbar(true);
  }

  function renderTableRow(data: ListingWithDataAndMediaCDto) {
    return (
      <>
        <TableCell>{data.createdOn}</TableCell>
        <TableCell>{data.fields?.JobNumber}</TableCell>
        <TableCell>{data.fields?.TowType}</TableCell>
        <TableCell>{data.fields?.CarRegistration}</TableCell>
        <TableCell>{data.fields?.CustomerName}</TableCell>
        <TableCell>{data.fields?.TransferAuthorised ? 'Yes' : 'No'}</TableCell>
        <TableCell>{data.fields?.TowedTo}</TableCell>
        <TableCell>{data.fields?.TruckId}</TableCell>
        <TableCell>{data.fields?.AllocatedBy}</TableCell>
      </>
    );
  }

  return (
    <div styleName="no-overflow">
      <div styleName="card">
        <DataTable<ListingWithDataAndMediaCDto, "listingId">
          primaryKeyProperty="listingId"
          title="Yard Log"
          tableId="base-yard-list"
          pageSize={10}
          initialSortColumn="creationDate"
          refreshTableTrigger={refreshTableTrigger}
          tableHeight={600}
          tableHeaders={tableHeaders}
          renderTableRow={renderTableRow}
          addButtonLabel="New Yard Log"
          addButtonOnClick={() => setIsAddDialogOpen(true)}
          onRowClick={(row) => navigate(`/Yard/${row.listingId}`)}
          callService={(params, search) => ListingService.getList(
            undefined,
            undefined,
            "JobDetail",
            undefined,
            undefined,
            undefined,
            undefined,
            undefined,
            undefined,
            ListingTypeEnum.Job,
            search,
            undefined,
            undefined,
            undefined,
            params
          )}
        />

        {isAddDialogOpen ?
        <DraggableDialog
          maxWidth="xl"
          title="Add New Yard Log"
          isOpen={isAddDialogOpen}
          onCancel={closeAddDialog}
          fullWidth={true}
        >
          <YardForm
            onCancel={closeAddDialog}
            onSave={onAddDialogSave}
          />
        </DraggableDialog> : null}

        <Snackbar
          open={showSnackbar}
          autoHideDuration={6000}
          onClose={() => setShowSnackbar(false)}
        >
          <Alert onClose={() => setShowSnackbar(false)} severity={snackbarSeverity}>
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </div>
    </div>
  );
}

export default YardList;