import { GetJobCDto, ListingWithDataAndMediaCDto, ManageListingWithDisplayGroupedDataAndMediaDto } from "redi-types";
import { Field, FieldProps, Formik, FormikHelpers, getIn } from "formik";
import { Box, CircularProgress, IconButton, MenuItem, TextField } from "@mui/material";
import { DatePicker } from '@mui/x-date-pickers';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import * as yup from "yup";
import "./styles.scss";
import ManageService from "../../../services/manage";
import { ListingTypeEnum } from "../../../enum/listingTypeEnum";
import { useEffect, useState } from "react";
import TowTypes from "../../../constants/TowTypes";
import { PhoneValidator } from "../../../utils/phoneValidator";
import ListingAutocompleteField from "../../../components/ListingAutocompleteField/ListingAutocompleteField";
import withAsyncLoad from "../../../components/HOC/withAsyncLoad";

const JobAutocompleteField = withAsyncLoad<any>(() => import('jobcomponents/JobAutocompleteField'));

type FormDto = {
  truck: {
    listingId: string;
    subject: string;
  } | null | undefined;
  job: {
    jobId: string,
    jobReference: string
  } | null;
  customerName: string;
  customerPhone: string;
  transferDate: Date | null;
  transferLocation: string;
  towType: string;
  allocatedBy: string;
  parentEntityId: string;
  carRegistration: string;
}

interface Props {
  referenceNo?: string;
  initialValues?: ManageListingWithDisplayGroupedDataAndMediaDto;
  onCancel?: () => void;
  onSave?: (value?: ManageListingWithDisplayGroupedDataAndMediaDto) => void;
  showHeader?: boolean;
}

export function YardForm(props: Props) {
  const [initialValues, setInitialValues] = useState<FormDto>();
  const [listing, setListing] = useState<ManageListingWithDisplayGroupedDataAndMediaDto>();

  const schema = yup.object().shape({
    truck: yup.object().shape({
      subject: yup.string().required("Required"),
      listingId: yup.string().required("Required"),
    }),
    job: yup.object().shape({
      jobReference: yup.string().required("Required"),
      jobId: yup.string().required("Required"),
    }),
    customerName: yup.string().required("Required"),
    customerPhone: yup.string()
      .required("Required")
      .test("phone-validation", "Invalid phone number", function(value) {
        if (!value) return false;
        const validationResult = PhoneValidator.validate(value);
        return validationResult === null;
      }),
    transferDate: yup.date().required("Required"),
    transferLocation: yup.string().required("Required"),
    towType: yup.string().required("Required"),
    allocatedBy: yup.string().required("Required"),
    parentEntityId: yup.string().required("Required")
  });

  useEffect(() => {
    async function fetch() {
      let values = props.initialValues;
      if (props.referenceNo) {
        const response = await ManageService.getListingByRef(props.referenceNo, false, true, true);
        if (!response.error && response.data) {
          values = response?.data;
        }
      }
      setInitialValues({
        customerName: "",
        customerPhone: "",
        transferLocation: "",
        towType: "",
        allocatedBy: "",
        parentEntityId: "",
        carRegistration: "",
        ...values?.fields,
        transferDate: values?.fields?.transferDate ? new Date(values.fields.transferDate) : new Date(),
        truck: values?.fields?.truckId ? {
          listingId: values?.fields?.truckListingId,
          subject: values?.fields?.truckId,
        } : null,
        job: values?.parentEntityId ? {
          jobId: values?.parentEntityId,
          jobReference: values?.fields?.jobNumber ?? "",
        } : null,
      });
      setListing(values);
    }
    fetch();
  }, [props.initialValues, props.referenceNo]);

  function cancel(form: any) {
    form.resetForm();
    props.onCancel && props.onCancel();
  }

  async function handleSubmit(values: FormDto, actions: FormikHelpers<FormDto>) {
    actions.setSubmitting(true);
    const formattedData = {
      ...ManageService.getInitialValues(),
      ...listing,
      parentEntityId: values.job?.jobId,
      subject: `Yard Log - ${values.customerName}`,
      description: `${values.customerName} | ${values.truck?.subject} | ${values.towType}`,
      listingTypeId: ListingTypeEnum.Job,
      fields: {
        truckId: values.truck?.subject,
        truckListingId: values?.truck?.listingId,
        customerName: values.customerName,
        customerPhone: values.customerPhone,
        transferDate: values.transferDate?.toISOString(),
        transferLocation: values.transferLocation,
        towType: values.towType,
        allocatedBy: values.allocatedBy,
        parentEntityId: values.parentEntityId,
        jobNumber: values.job?.jobReference,
      }
    };

    const response = listing?.listingId
      ? await ManageService.updateListingWDP(formattedData)
      : await ManageService.createListingWDP(formattedData, false);

    if (!response.error) {
      props.onSave && props.onSave(response.data);
    }
    actions.setSubmitting(false);
  }

  return (
    !initialValues ?
    <Box
      display="flex"
      height="100%"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      textAlign="center"
    >
      <CircularProgress size={25} />
    </Box> :
    <Formik
      initialValues={initialValues}
      validationSchema={schema}
      onSubmit={handleSubmit}
    >
      {(form) => (
        <form onSubmit={form.handleSubmit}>
          <div styleName="container">
            {props.showHeader ? (
              <div styleName="row">
                <div styleName="header">Yard Log Details</div>
                <div styleName="row">
                  <IconButton onClick={() => form.handleSubmit()}>
                    <FontAwesomeIcon icon="check" />
                  </IconButton>
                  <IconButton onClick={() => cancel(form)}>
                    <FontAwesomeIcon icon="close" />
                  </IconButton>
                </div>
              </div>
            ) : null}
            <div styleName="form-grid">
              <div styleName="row">
                <div styleName="field-container size-1">
                  <Field name="job">
                    {(fieldProps: FieldProps) => (
                      <JobAutocompleteField
                        id="job"
                        name={fieldProps.field.name}
                        label="Job"
                        value={form.values.job as any}
                        initialDisplayText={form.values.job?.jobReference ?? ""}
                        autoSelectFirst={true}
                        textFieldProps={{
                          variant: "outlined"
                        }}
                        onChange={(val: string, job: GetJobCDto) => {
                          form.setFieldValue("job", job);
                          form.setFieldValue("carRegistration", job?.carRegistration);
                        }}
                        error={Boolean(getIn(form.touched, `job`) && getIn(form.errors, `job`))}
                        helperText={getIn(form.touched, `job`) && getIn(form.errors, `job`)} />
                    )}
                  </Field>
                </div>
              </div>
              <div styleName="row">
                <div styleName="field-container size-1">
                  <Field name="truck">
                    {(fieldProps: FieldProps) => (
                      <ListingAutocompleteField
                        id="truck"
                        name={fieldProps.field.name}
                        label="Truck"
                        value={form.values.truck as unknown as ListingWithDataAndMediaCDto}
                        initialDisplayText={form.values.truck?.subject ?? ""}
                        fieldValue="listingId"
                        fieldDisplayText="description"
                        autoSelectFirst={true}
                        params={{
                          listingTypeId: ListingTypeEnum.Truck
                        }}
                        textFieldProps={{
                          variant: "outlined"
                        }}
                        onChange={(val, listing) => {
                          form.setFieldValue("truck", listing);
                        }}
                        error={Boolean(getIn(form.touched, `truck`) && getIn(form.errors, `truck`))}
                        helperText={getIn(form.touched, `truck`) && getIn(form.errors, `truck`)} />
                    )}
                  </Field>
                </div>
              </div>
              <div styleName="row">
                <div styleName="field-container size-1">
                  <Field
                    fullWidth
                    name="customerName"
                    variant="standard"
                    label="Customer Name"
                    as={TextField}
                    error={Boolean(
                      getIn(form.touched, "customerName") &&
                      getIn(form.errors, "customerName")
                    )}
                    helperText={
                      getIn(form.touched, "customerName") &&
                      getIn(form.errors, "customerName")
                    }
                  />
                </div>
              </div>
              <div styleName="row">
                <div styleName="field-container size-1">
                  <Field
                    fullWidth
                    name="customerPhone"
                    variant="standard"
                    label="Customer Phone"
                    as={TextField}
                    error={Boolean(
                      getIn(form.touched, "customerPhone") &&
                      getIn(form.errors, "customerPhone")
                    )}
                    helperText={
                      getIn(form.touched, "customerPhone") &&
                      getIn(form.errors, "customerPhone")
                    }
                  />
                </div>
              </div>
              <div styleName="row">
                <div styleName="field-container size-1">
                  <DatePicker
                    label="Transfer Date"
                    inputFormat="DD/MM/YYYY"
                    value={form.values.transferDate}
                    onChange={(val) => form.setFieldValue("transferDate", val)}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="standard"
                        fullWidth
                        error={Boolean(
                          getIn(form.touched, "transferDate") &&
                          getIn(form.errors, "transferDate")
                        )}
                        helperText={
                          getIn(form.touched, "transferDate") &&
                          getIn(form.errors, "transferDate")
                        }
                      />
                    )}
                  />
                </div>
              </div>
              <div styleName="row">
                  <div styleName="field-container size-1">
                    <Field
                      select
                      fullWidth
                      name="towType"
                      variant="standard"
                      label="Tow Type"
                      as={TextField}
                      error={Boolean(
                        getIn(form.touched, "towType") &&
                        getIn(form.errors, "towType")
                      )}
                      helperText={
                        getIn(form.touched, "towType") &&
                        getIn(form.errors, "towType")
                      }
                    >
                      {TowTypes.map((item) => (
                        <MenuItem key={item.code} value={item.code}>
                          {item.description}
                        </MenuItem>
                      ))}
                    </Field>
                  </div>
              </div>
              <div styleName="row">
                <div styleName="field-container size-1">
                  <Field
                    fullWidth
                    name="transferLocation"
                    variant="standard"
                    label="Transfer Location"
                    as={TextField}
                    error={Boolean(
                      getIn(form.touched, "transferLocation") &&
                      getIn(form.errors, "transferLocation")
                    )}
                    helperText={
                      getIn(form.touched, "transferLocation") &&
                      getIn(form.errors, "transferLocation")
                    }
                  />
                </div>
              </div>
              <div styleName="row">
                <div styleName="field-container size-1">
                  <Field
                    fullWidth
                    name="allocatedBy"
                    variant="standard"
                    label="Allocated By"
                    as={TextField}
                    error={Boolean(
                      getIn(form.touched, "allocatedBy") &&
                      getIn(form.errors, "allocatedBy")
                    )}
                    helperText={
                      getIn(form.touched, "allocatedBy") &&
                      getIn(form.errors, "allocatedBy")
                    }
                  />
                </div>
              </div>
            </div>
          </div>
        </form>
      )}
    </Formik>
  );
}
